package com.frt.usercore.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.frt.usercore.domain.dto.param.RoleListQueryParamDTO;
import com.frt.usercore.domain.dto.result.FindRoleByUserIdListResultDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 租户角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface TenantRoleMapper extends BaseMapper<TenantRoleDO> {

	/**
	 * 分页查询角色列表
	 *
	 * @param page 分页参数
	 * @return 分页结果
	 */
	Page<TenantRoleDO> findPageList(Page<RoleListQueryParamDTO> page, @Param("query") RoleListQueryParamDTO query);

	/**
	 * 根据 userId 查询角色名称
	 * @param userIdList 用户id列表
	 * @return 角色名称
	 */
	List<FindRoleByUserIdListResultDTO> findRoleByUserIdList(@Param("userIdList") List<String> userIdList);
}
