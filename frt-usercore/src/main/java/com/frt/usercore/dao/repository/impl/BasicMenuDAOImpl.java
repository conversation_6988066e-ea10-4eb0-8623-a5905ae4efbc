package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.dao.entity.BasicMenuDO;
import com.frt.usercore.dao.mapper.BasicMenuMapper;
import com.frt.usercore.dao.repository.BasicMenuDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 导航菜单表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
public class BasicMenuDAOImpl extends ServiceImpl<BasicMenuMapper, BasicMenuDO> implements BasicMenuDAO {

    /**
     * 根据用户id查询菜单
     * @param userId
     * @return
     */
    @Override
    public List<BasicMenuDO> selectMenuByUserId(String userId) {
        return getBaseMapper().selectMenuByUserId(userId);
    }

}
