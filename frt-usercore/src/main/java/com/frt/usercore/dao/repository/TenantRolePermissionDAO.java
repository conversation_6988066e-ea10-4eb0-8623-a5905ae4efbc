package com.frt.usercore.dao.repository;

import com.frt.usercore.dao.entity.BasicPermissionDO;
import com.frt.usercore.dao.entity.TenantRolePermissionDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 租户角色权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface TenantRolePermissionDAO extends IService<TenantRolePermissionDO> {

    /**
     * 查询租户权限
     *
     * @param tenantId
     * @return
     */
    List<BasicPermissionDO> selectTenantPermissionByTenantId(String tenantId);

    /**
     * 查询租户角色权限
     * @param tenantId
     * @param roleId
     * @return
     */
    List<BasicPermissionDO> selectTenantRolePermissionByTenantIdAndRoleId(String tenantId, String roleId);

    /**
     * 删除角色权限
     *
     * @param tenantId
     * @param roleId
     */
    boolean removeByTenantIdAndRoleId(String tenantId, String roleId);
}
