package com.frt.usercore.dao.mapper;

import com.frt.usercore.dao.entity.BasicPermissionDO;
import com.frt.usercore.dao.entity.TenantRolePermissionDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * <p>
 * 租户角色权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface TenantRolePermissionMapper extends BaseMapper<TenantRolePermissionDO> {

    /**
     * 查询租户权限
     * @param tenantId
     * @return
     */
    List<BasicPermissionDO> selectTenantPermissionByTenantId(String tenantId);

    /**
     * 查询租户角色权限
     * @param tenantId
     * @param roleId
     * @return
     */
    List<BasicPermissionDO> selectTenantPermissionByTenantIdAndRoleId(String tenantId, String roleId);

}
