package com.frt.usercore.dao.repository.impl;

import com.frt.usercore.common.enums.business.DelFlagEnum;
import com.frt.usercore.dao.entity.BasicPermissionDO;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.frt.usercore.dao.entity.TenantRolePermissionDO;
import com.frt.usercore.dao.mapper.TenantRolePermissionMapper;
import com.frt.usercore.dao.repository.TenantRolePermissionDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 租户角色权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
@Service
public class TenantRolePermissionDAOImpl extends ServiceImpl<TenantRolePermissionMapper, TenantRolePermissionDO> implements TenantRolePermissionDAO {

    @Override
    public List<BasicPermissionDO> selectTenantPermissionByTenantId(String tenantId) {
        return getBaseMapper().selectTenantPermissionByTenantId(tenantId);
    }

    /**
     * 查询租户角色权限
     *
     * @param tenantId
     * @param roleId
     * @return
     */
    @Override
    public List<BasicPermissionDO> selectTenantRolePermissionByTenantIdAndRoleId(String tenantId, String roleId) {
        return getBaseMapper().selectTenantPermissionByTenantIdAndRoleId(tenantId, roleId);
    }

    /**
     * 删除角色权限
     *
     * @param tenantId
     * @param roleId
     */
    @Override
    public boolean removeByTenantIdAndRoleId(String tenantId, String roleId) {
        return update()
                .set(TenantRolePermissionDO.IS_DEL, DelFlagEnum.DELETED.getCode())
                .eq(TenantRolePermissionDO.ROLE_ID, roleId)
                .eq(TenantRolePermissionDO.TENANT_ID, tenantId)
                .update();
    }
}
