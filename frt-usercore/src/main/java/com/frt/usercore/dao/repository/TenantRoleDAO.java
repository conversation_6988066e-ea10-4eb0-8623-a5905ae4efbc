package com.frt.usercore.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.frt.usercore.dao.entity.TenantRoleDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.frt.usercore.domain.dto.param.RoleListQueryParamDTO;
import com.frt.usercore.domain.dto.result.FindRoleByUserIdListResultDTO;
import com.frt.usercore.domain.param.PageParam;

import java.util.List;

/**
 * <p>
 * 租户角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-28
 */
public interface TenantRoleDAO extends IService<TenantRoleDO> {

    /**
     * 根据角色ID查询角色信息
     * @param roleId 角色ID
     * @return 角色信息
     */
    TenantRoleDO getByRoleId(String roleId);

	/**
	 * 分页查询角色列表
	 *
	 * @param pageDTO 分页参数
	 * @return 分页结果
	 */
	Page<TenantRoleDO> findPageList(PageParam<RoleListQueryParamDTO> pageDTO);

	/**
	 * 根据角色名称查询角色
	 * @param roleName 角色名称
	 * @param tenantId 租户ID
	 * @param roleType 角色类型 1-运营 2-代理商 3-商户
	 * @param roleId 排除的角色 id
	 * @return 角色
	 */
	TenantRoleDO getByRoleName(String roleName, String tenantId, Integer roleType, String roleId);

	/**
	 * 根据角色ID删除角色
	 * @param roleId
	 * @return
	 */
	boolean removeByRoleId(String roleId);

	/**
	 * 根据 userId 查询角色名称
	 * @param userIdList 用户id列表
	 * @return 角色名称
	 */
	List<FindRoleByUserIdListResultDTO> findRoleByUserIdList(List<String> userIdList);

	/**
	 * 根据租户ID和角色ID查询角色
	 * @param tenantId
	 * @param roleId
	 * @return
	 */
	TenantRoleDO getByTenantIdAndRoleIdAndRoleType(String tenantId, String roleId, Integer roleType);
}
