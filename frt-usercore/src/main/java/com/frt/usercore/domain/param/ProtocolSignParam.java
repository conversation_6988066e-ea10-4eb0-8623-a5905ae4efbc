/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.domain.param;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version ProtocolSignParam.java, v 0.1 2025-08-29 14:22 zhangling
 */
@Data
public class ProtocolSignParam {

    /**
     * 用户id
     */
    @NotBlank(message = "用户id不能为空")
    private String userId;
    /**
     * 协议id列表
     */
    @NotNull(message = "协议id列表不能为空")
    private List<String> protocolIdList;

    /**
     * 平台类型 1-运营后台 2-代理商 3-商户
     */
    @NotNull(message = "平台类型不能为空")
    private Integer platformType;
}