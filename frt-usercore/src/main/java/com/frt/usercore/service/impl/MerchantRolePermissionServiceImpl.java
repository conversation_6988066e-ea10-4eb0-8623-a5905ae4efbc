/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import com.frt.usercore.domain.param.rolemanager.MerchantMenuPermissionParam;
import com.frt.usercore.domain.result.rolemanager.MerchantRolePermissionListResult;
import com.frt.usercore.service.MerchantRolePermissionService;
import com.frt.usercore.service.RoleManagerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version MerchantRolePermissionServiceImpl.java, v 0.1 2025-08-29 16:08 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantRolePermissionServiceImpl implements MerchantRolePermissionService {

    private final RoleManagerService roleManagerService;

    /**
     * 获取权限模板
     *
     * @param param
     * @return
     */
    @Override
    public MerchantRolePermissionListResult getPermissionTemplate(MerchantMenuPermissionParam param) {
        return null;
    }
}