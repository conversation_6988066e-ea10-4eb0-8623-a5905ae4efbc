/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service;

import com.frt.usercore.domain.param.rolemanager.MerchantMenuPermissionParam;
import com.frt.usercore.domain.result.rolemanager.MerchantRolePermissionListResult;

/**
 * <AUTHOR>
 * @version MerchantRolePermissionService.java, v 0.1 2025-08-29 16:08 zhangling
 */
public interface MerchantRolePermissionService {

    /**
     * 获取权限模板
     * @param param
     * @return
     */
    MerchantRolePermissionListResult getPermissionTemplate(MerchantMenuPermissionParam param);
}