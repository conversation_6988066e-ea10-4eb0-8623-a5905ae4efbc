package com.frt.usercore.service.impl.common;

import com.frt.usercore.service.common.SmsCommonService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class SmsCommonServiceImpl implements SmsCommonService {


	/**
	 * 发送短信
	 *
	 * @param phone      手机号
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 */
	@Override
	public Boolean sendSms(String phone, String sceneValue, String tenantId) {
		return null;
	}

	/**
	 * 校验短信验证码
	 *
	 * @param phone      手机号
	 * @param code       验证码
	 * @param sceneValue 场景值
	 * @param tenantId   租户ID
	 */
	@Override
	public Boolean checkSmsCode(String phone, String code, String sceneValue, String tenantId) {
		return null;
	}
}
