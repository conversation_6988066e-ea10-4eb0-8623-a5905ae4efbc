/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.frt.usercore.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.frt.usercore.common.enums.business.PlatformEnum;
import com.frt.usercore.common.utils.ValidateUtil;
import com.frt.usercore.dao.entity.AccountDO;
import com.frt.usercore.dao.entity.ProtocolConfigDO;
import com.frt.usercore.dao.entity.UserProtocolSignDO;
import com.frt.usercore.dao.repository.AccountDAO;
import com.frt.usercore.dao.repository.ProtocolConfigDAO;
import com.frt.usercore.dao.repository.UserProtocolSignDAO;
import com.frt.usercore.domain.mapper.ProtocolMapper;
import com.frt.usercore.domain.param.ProtocolInfoParam;
import com.frt.usercore.domain.param.ProtocolListQueryParam;
import com.frt.usercore.domain.param.ProtocolSignParam;
import com.frt.usercore.domain.result.ListResult;
import com.frt.usercore.domain.result.base.ValidateResult;
import com.frt.usercore.domain.result.protocol.ProtocolInfoResult;
import com.frt.usercore.service.ProtocolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

/**
 * <AUTHOR>
 * @version ProtocolServiceImpl.java, v 0.1 2025-08-28 10:08 zhangling
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProtocolServiceImpl implements ProtocolService {

    private final ProtocolConfigDAO protocolConfigDAO;

    private final UserProtocolSignDAO userProtocolSignDAO;

    private final AccountDAO accountDAO;

    private final ProtocolMapper protocolMapper;

    /**
     * 查询协议列表
     *
     * @param param
     * @return
     */
    @Override
    public ListResult<ProtocolInfoResult> findProtocolList(ProtocolListQueryParam param) {
        final AccountDO accountDO = accountDAO.getByAccountAndPasswordAndPlatformType(param.getAccount(), param.getPassword(), PlatformEnum.MERCHANT.getCode());
        if (ObjectUtil.isNull(accountDO)) {
            return new ListResult<>();
        }
        List<ProtocolConfigDO> protocolList = protocolConfigDAO.findByTerminalUserType(PlatformEnum.MERCHANT.getCode());
        // 已签署的协议列表
        final List<UserProtocolSignDO> signedList = userProtocolSignDAO.findByUserId(accountDO.getUserId());
        if (CollectionUtil.isNotEmpty(signedList)) {
            protocolList = protocolList.stream().filter(protocol -> signedList.stream().anyMatch(signed -> signed.getProtocolId().equals(protocol.getProtocolId()))).toList();
        }
        return new ListResult<>(protocolList.stream().map(protocolMapper::coverProtocolConfigDOToProtocolInfoResult).toList());
    }

    /**
     * 查询协议内容
     *
     * @param param
     * @return
     */
    @Override
    public ProtocolInfoResult getProtocolInfo(ProtocolInfoParam param) {
        final AccountDO accountDO = accountDAO.getByAccountAndPasswordAndPlatformType(param.getAccount(), param.getPassword(), PlatformEnum.MERCHANT.getCode());
        if (ObjectUtil.isNull(accountDO)) {
            throw new RuntimeException("账号或密码错误");
        }
        final ProtocolConfigDO protocolConfigDO = protocolConfigDAO.getById(param.getProtocolId());
        if (ObjectUtil.isNull(protocolConfigDO)) {
            throw new RuntimeException("协议不存在");
        }
        return protocolMapper.coverProtocolConfigDOToProtocolInfoResult(protocolConfigDO);
    }

    /**
     * 协议签署
     *
     * @param param
     * @return
     */
    @Override
    public boolean protocolSign(ProtocolSignParam param) {
        log.info("protocolSign >> 协议签署开始 param = {}", param);
        final ValidateResult validateResult = ValidateUtil.baseValidate(param);
        if (!validateResult.isResult()) {
            return Boolean.FALSE;
        }
        final PlatformEnum platformEnum = PlatformEnum.getByCode(param.getPlatformType());
        if (platformEnum == null) {
            log.info("protocolSign >> 平台类型不存在 param = {}", param);
            return Boolean.FALSE;
        }
        // 查询用户需要签署的协议列表
        final List<ProtocolConfigDO> protocolList = protocolConfigDAO.findByTerminalUserType(platformEnum.getCode());
        if (CollectionUtil.isEmpty(protocolList)) {
            log.warn("protocolSign >> 无协议配置 param = {}", param);
            return Boolean.TRUE;
        }
        // 查询用户当前签署协议列表
        final List<UserProtocolSignDO> signedList = userProtocolSignDAO.findByUserId(param.getUserId());
        if (CollectionUtil.isNotEmpty(signedList)) {
            protocolList.removeIf(protocol -> signedList.stream().anyMatch(signed -> signed.getProtocolId().equals(protocol.getProtocolId())));
        }
        //待签署协议列表
        final List<String> noSignProtocolIdList = protocolList.stream().map(ProtocolConfigDO::getProtocolId).distinct().toList();
        if (CollectionUtil.isEmpty(noSignProtocolIdList)) {
            log.info("protocolSign >> 无待签署协议 param = {}", param);
            return Boolean.TRUE;
        }
        boolean allMatch = new HashSet<>(param.getProtocolIdList()).containsAll(noSignProtocolIdList);
        if (!allMatch) {
            log.warn("protocolSign >> 协议列表有误 param = {}", param);
            return Boolean.FALSE;
        }
        //协议签署
        List<UserProtocolSignDO> saveList = new ArrayList<>(noSignProtocolIdList.size());
        for (String protocolId : noSignProtocolIdList) {
            final UserProtocolSignDO userProtocolSignDO = new UserProtocolSignDO();
            userProtocolSignDO.setProtocolId(protocolId);
            userProtocolSignDO.setUserId(param.getUserId());
            userProtocolSignDO.setUserType(platformEnum.getCode());
            userProtocolSignDO.setSignTime(DateUtil.millisecond(new Date())/1000);
            saveList.add(userProtocolSignDO);
        }
        userProtocolSignDAO.saveBatch(saveList);
        log.info("protocolSign >> 协议签署结束 param = {}", param);
        return Boolean.TRUE;
    }
}