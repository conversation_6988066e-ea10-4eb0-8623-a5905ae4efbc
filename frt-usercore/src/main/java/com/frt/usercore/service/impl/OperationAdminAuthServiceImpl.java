package com.frt.usercore.service.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.stp.parameter.SaLoginParameter;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.frt.usercore.common.constants.RedisPrefixConstant;
import com.frt.usercore.common.enums.business.*;
import com.frt.usercore.common.enums.exception.AuthErrorEnum;
import com.frt.usercore.dao.entity.*;
import com.frt.usercore.dao.repository.*;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminLoginResult;
import com.frt.usercore.domain.result.operationadmin.auth.OperationAdminResourceResult;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminChangePasswordParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminCheckCodeParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminLoginParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminResourceParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminSearchPhoneParam;
import com.frt.usercore.domain.param.operationadmin.auth.OperationAdminSendCodeParam;
import com.frt.usercore.domain.result.protocol.ProtocolInfoResult;
import com.frt.usercore.service.OperationAdminAuthService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;


import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 运营后台权限服务实现类
 */
@Slf4j
@Service
public class OperationAdminAuthServiceImpl implements OperationAdminAuthService {

    @Resource
    private AccountDAO accountDAO;

    @Resource
    private TenantRolePermissionDAO tenantRolePermissionDAO;

    @Resource
    private TenantDAO tenantDAO;

    @Resource
    private BasicMenuDAO basicMenuDAO;

    @Resource
    private ProtocolConfigDAO protocolConfigDAO;

    @Resource
    private TenantResourceDAO resourceDAO;

    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    // 密码错误次数key前缀
    private static final String PASSWORD_ERROR_COUNT_KEY_PREFIX = "user:password:error:count:";
    
    // 密码错误锁定key前缀
    private static final String PASSWORD_ERROR_LOCK_KEY_PREFIX = "user:password:error:lock:";
    
    // 密码错误次数限制
    private static final int MAX_PASSWORD_ERROR_COUNT = 5;
    
    // 密码错误锁定时间(分钟)
    private static final int PASSWORD_ERROR_LOCK_MINUTES = 10;
    
    // 密码错误计数过期时间(分钟)
    private static final int PASSWORD_ERROR_COUNT_EXPIRE_MINUTES = 30;
    
    // 是否允许多点登录
    @Value("${sa-token.is-concurrent: false}")
    private Boolean isConcurrent;
    
    // 同时登录设备最大数量
    @Value("${sa-token.max-login-count: 1}")
    private Integer maxLoginCount;
    
    // token过期时间(秒)
    @Value("${sa-token.timeout: 2592000}")
    private Long timeout;

    /**
     * 4.1 登录页资源获取接口
     * 接口名称：operation/web/search/resource
     * 请求方式：POST
     *
     * @param param 请求参数 webAddress 二级域名
     * @return 资源信息
     */
    @Override
    public OperationAdminResourceResult searchResource(OperationAdminResourceParam param) {
        OperationAdminResourceResult result = new OperationAdminResourceResult();
        // logo
        String brandLogo = StringUtils.EMPTY;
        // 背景图
        String backgroundImage = StringUtils.EMPTY;
        // 主题色
        String themeColor = StringUtils.EMPTY;

        result.setSendCode(false);
        //根据域名查询租户信息
        TenantResourceDO tenantResourceDO = resourceDAO.getBaseMapper().selectOne(new LambdaQueryWrapper<TenantResourceDO>()
                .eq(TenantResourceDO::getLoginPortType, LoginPortEnum.OPERATION.getCode())
                .eq(TenantResourceDO::getResourceType, ResourceTypeEnum.DOMAIN_NAME.getCode())
                .eq(TenantResourceDO::getIsDel, DelFlagEnum.NOT_DELETED));
        if (ObjectUtil.isNull(tenantResourceDO)) {
            throw AuthErrorEnum.DOMAIN_NAME_EXIST.exception();
        }
        List<TenantResourceDO> resources = resourceDAO.getBaseMapper().selectList(new LambdaQueryWrapper<TenantResourceDO>()
                .in(TenantResourceDO::getLoginPortType, Arrays.asList(LoginPortEnum.ALL.getCode(), LoginPortEnum.OPERATION.getCode()))
                .eq(TenantResourceDO::getIsDel, DelFlagEnum.NOT_DELETED));
        if (CollectionUtil.isEmpty( resources)) {
            throw AuthErrorEnum.RESOURCE_NOT_EXIST.exception();
        }
        // 获取背景图
        List<TenantResourceDO> backgrounds = resources.stream().filter(resource -> ResourceTypeEnum.BACK_GROUND.getCode().equals(resource.getResourceType())).toList();
        if (CollectionUtil.isNotEmpty(backgrounds)) {
            backgroundImage = backgrounds.get(0).getResourceValue();
        }
        // 获取logo
        List<TenantResourceDO> logos = resources.stream().filter(resource -> ResourceTypeEnum.LOGO.getCode().equals(resource.getResourceType())).toList();
        if (CollectionUtil.isNotEmpty(logos)) {
            brandLogo = logos.get(0).getResourceValue();
        }
        // 获取主题色
        List<TenantResourceDO> themes = resources.stream().filter(resource -> ResourceTypeEnum.THEME_COLOR.getCode().equals(resource.getResourceType())).toList();
        if (CollectionUtil.isNotEmpty(themes)) {
            themeColor = themes.get(0).getResourceValue();
        }
        //
        List<ProtocolConfigDO> byTerminalUserType = protocolConfigDAO.findByTerminalUserType(PlatformEnum.OPERATION.getCode());
        if (CollectionUtil.isNotEmpty(byTerminalUserType)) {
            List<OperationAdminResourceResult.ProtocolInfo> protocolInfoResults = BeanUtil.copyToList(byTerminalUserType, OperationAdminResourceResult.ProtocolInfo.class);
            result.setProtocolList(protocolInfoResults);
        }
        result.setBrandLogo(brandLogo);
        result.setBackgroundImage(backgroundImage);
        result.setThemeColor(themeColor);
        // 随机生成一个token，把租户id存储到缓存中
        String token = java.util.UUID.randomUUID().toString().replace("-", "");
        String key = StrUtil.format(RedisPrefixConstant.BEFORE_LOGIN_SESSION_TOKEN , token);
        stringRedisTemplate.opsForValue().set(key, tenantResourceDO.getTenantId(), 24, java.util.concurrent.TimeUnit.HOURS);
        result.setToken(token);
        return result;
    }

    /**
     * 4.2 发送验证码
     * 接口名称：operation/web/send/code
     * 请求方式：POST
     *
     * @param param 请求参数
     *              tenantId 租户id
     *              phone 手机号
     *              type 场景类型 1-登录 2-修改密码
     */
    @Override
    public void sendCode(OperationAdminSendCodeParam param) {
        // TODO: 实现具体业务逻辑
        // 示例：调用短信服务发送验证码
    }

    /**
     * 4.3 账号登录
     * 接口名称：operation/web/login
     * 请求方式：POST
     *
     * @param param 登录参数
     *              webAddress 二级域名
     *              tenantId 租户Id
     *              type 登录方式 1-密码登录 2-验证码登录
     *              account 账号
     *              password 密码（md5加密）
     *              code 验证码
     * @return 登录结果
     */
    @Override
    public OperationAdminLoginResult login(OperationAdminLoginParam param) {
        AccountDO accountDO = new AccountDO();
        if (1 == param.getType()) {
            //密码登录
            if (StringUtils.isBlank(param.getAccount()) || StringUtils.isBlank(param.getPassword()) || StringUtils.isBlank(param.getTenantId())) {
                throw AuthErrorEnum.ACCOUNT_NOT_EXIST.exception();
            }
            accountDO = accountDAO.selectByAccountAndPlatformType(param.getAccount(), PlatformEnum.OPERATION.getCode(), param.getTenantId());
            if (ObjectUtil.isNull(accountDO)) {
                throw AuthErrorEnum.ACCOUNT_NOT_EXIST.exception();
            }
            // 检查账号状态
            if (!accountDO.getAccountStatus().equals(AccountStatusEnum.NORMAL.getCode())) {
                throw AuthErrorEnum.ACCOUNT_NOT_ACTIVE.exception();
            }
        } else {
            throw AuthErrorEnum.SIGN_INVALID.exception();
        }
        // 是否支持多点登录
        boolean isConcurrent = true;
        // 同时最多登录数
        int maxLoginCount = 30;
        // 登录有效期
        int timeout = 60 * 60 * 24 * 7;
        List<BasicPermissionDO> basicPermissions = tenantRolePermissionDAO.selectTenantPermissionByTenantId(param.getTenantId());
        // 权限为空时使用默认权限
        if (CollectionUtil.isNotEmpty(basicPermissions)) {
            // 判断是否启用多点登录
            List<BasicPermissionDO> manyLog = basicPermissions.stream().filter(permission -> permission.getPermissionType().equals(PermissionEnum.MULTI_LOGIN.getCode())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(manyLog)) {
                String value = manyLog.get(0).getPermissionValue();
                // value为1时启用多点登录,否则不启用多点登录
                if (!"1".equals(value)) {
                    isConcurrent = false;
                }
            }
            // 获取同时登录设备最大数
            List<BasicPermissionDO> maxLogin = basicPermissions.stream().filter(permission -> permission.getPermissionType().equals(PermissionEnum.MAX_LOGIN_DEVICES.getCode())).toList();
            if (CollectionUtil.isNotEmpty(maxLogin)) {
                String value = maxLogin.get(0).getPermissionValue();
                maxLoginCount = Integer.parseInt(value);
            }
            // 获取登录有效期
            List<BasicPermissionDO> loginValidity = basicPermissions.stream().filter(permission -> permission.getPermissionType().equals(PermissionEnum.LOGIN_VALIDITY.getCode())).toList();
            if (CollectionUtil.isNotEmpty(loginValidity)) {
                String value = loginValidity.get(0).getPermissionValue();
                timeout = Integer.parseInt(value);
            }
        }
        // 处理多点登录逻辑
        if (!isConcurrent) {
            // 不允许多点登录，踢掉已登录的设备
            StpUtil.logout(accountDO.getUserId());
        } else if (maxLoginCount > 0) {
            // 检查当前登录设备数量
            List<String> tokenList = StpUtil.getTokenValueListByLoginId(accountDO.getUserId());
            if (tokenList.size() >= maxLoginCount) {
                // 超出最大登录设备数，踢掉最早登录的设备
                StpUtil.logoutByTokenValue(tokenList.get(0));
            }
        }

        SaLoginParameter saLoginParameter = new SaLoginParameter();
        // 设置过期时间
        saLoginParameter.setTimeout(timeout);
        // 是否多点登录
        saLoginParameter.setIsConcurrent(isConcurrent);
        // 同时最大登录数
        saLoginParameter.setMaxLoginCount(maxLoginCount);
        // 登录
        StpUtil.login(accountDO.getUserId(), saLoginParameter);
        // 示例数据，实际应进行用户验证并生成token
        OperationAdminLoginResult result = new OperationAdminLoginResult();
        String accessToken = StpUtil.getTokenValue();
        result.setToken(accessToken);
        // 账号信息
        OperationAdminLoginResult.UserInfo userInfo = new OperationAdminLoginResult.UserInfo();
        userInfo.setUserId(accountDO.getUserId());
        userInfo.setAccount(accountDO.getAccount());
        userInfo.setIsAdmin(accountDO.getIsAdmin());
        userInfo.setName(accountDO.getName());
        userInfo.setPhone(accountDO.getPhone());
        result.setUserInfo(userInfo);
        // 租户信息
        OperationAdminLoginResult.TenantInfo tenantInfo = new OperationAdminLoginResult.TenantInfo();
        TenantDO tenantDO = tenantDAO.getBaseMapper().selectOne(new LambdaQueryWrapper<TenantDO>().eq(TenantDO::getTenantId, accountDO.getTenantId()));
        if (ObjectUtil.isNotNull(tenantDO)) {
            tenantInfo.setTenantId(tenantDO.getTenantId());
            tenantInfo.setTenantName(tenantDO.getTenantName());
            tenantInfo.setPhone(tenantDO.getPhone());
            result.setTenantInfo(tenantInfo);
        }
        List<BasicMenuDO> basicMenuDOS = basicMenuDAO.selectMenuByUserId(accountDO.getUserId());
        if (CollectionUtil.isNotEmpty(basicMenuDOS)) {
            // 菜单列表
            List<String> menuList = basicMenuDOS.stream().filter(basicMenuDO -> basicMenuDO.getMenuType().equals(1) && LoginPortEnum.OPERATION.getCode().equals(basicMenuDO.getLoginPortType()) && basicMenuDO.getIsVisible().equals(1))
                    .map(BasicMenuDO::getMenuCode).toList();
            result.setMenuList(menuList);
            // 功能列表
            List<String> funcList = basicMenuDOS.stream().filter(basicMenuDO -> basicMenuDO.getMenuType().equals(2) && LoginPortEnum.OPERATION.getCode().equals(basicMenuDO.getLoginPortType()) && basicMenuDO.getIsVisible().equals(1))
                    .map(BasicMenuDO::getMenuCode).toList();
            result.setFuncList(funcList);
            // api列表
            List<String> apiList = basicMenuDOS.stream().filter(basicMenuDO -> LoginPortEnum.OPERATION.getCode().equals(basicMenuDO.getLoginPortType())).map(BasicMenuDO::getApiPath).toList();
            result.setApiPath(apiList);
        }
        List<ProtocolConfigDO> byTerminalUserType = protocolConfigDAO.findByTerminalUserType(PlatformEnum.OPERATION.getCode());
        if (CollectionUtil.isNotEmpty(byTerminalUserType)) {
            List<OperationAdminLoginResult.ProtocolInfo> protocolInfoResults = BeanUtil.copyToList(byTerminalUserType, OperationAdminLoginResult.ProtocolInfo.class);
            result.setProtocolList(protocolInfoResults);
        }
        StpUtil.getTokenSessionByToken(accessToken).set(accessToken, result);
        return result;
    }

    /**
     * 4.4 通过账号查询加密手机号
     * 接口名称：operation/web/search/phone
     * 请求方式：POST
     *
     * @param param 查询参数 account 账号
     * @return 手机号信息
     */
    @Override
    public String searchPhone(OperationAdminSearchPhoneParam param) {
        // TODO: 实现具体业务逻辑
        // 示例数据，实际应从数据库查询用户信息
        return "138****8888";
    }

    /**
     * 4.5 修改密码验证码校验
     * 接口名称：operation/web/check/code
     * 请求方式：POST
     *
     * @param param 验证参数
     *              code 验证码
     *              account 账号
     */
    @Override
    public void checkCode(OperationAdminCheckCodeParam param) {
        // TODO: 实现具体业务逻辑
        // 示例：校验验证码是否正确
    }

    /**
     * 4.6 设置新密码
     * 接口名称：operation/web/change/password
     * 请求方式：POST
     *
     * @param param 修改密码参数
     *              account 账号
     *              password 新密码
     *              secondPassword 新密码确认
     */
    @Override
    public void changePassword(OperationAdminChangePasswordParam param) {
        // TODO: 实现具体业务逻辑
        // 示例：更新用户密码
    }

    /**
     * 4.7 账号登出
     * 接口名称：operation/web/logout
     * 请求方式：POST
     */
    @Override
    public void logout() {
        // TODO: 实现具体业务逻辑
        // 示例：清理用户登录状态
        StpUtil.logout();
    }
}
