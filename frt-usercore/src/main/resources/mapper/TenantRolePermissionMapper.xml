<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.TenantRolePermissionMapper">

    <select id="selectTenantPermissionByTenantId" resultType="com.frt.usercore.dao.entity.BasicPermissionDO">
        select b.permission_id,
               b.permission_name,
               b.permission_value,
               b.permission_type,
               b.permission_business_type,
               b.login_port_type
        from frt_tenant_role_permission a
                 left join frt_basic_permission b on a.permission_id = b.permission_id
        where a.tenant_id = #{tenantId, jdbcType=VARCHAR}
          and a.role_id = ''
          and b.is_enabled = 1
    </select>

    <select id="selectTenantPermissionByTenantIdAndRoleId" resultType="com.frt.usercore.dao.entity.BasicPermissionDO">
        select b.permission_id,
               b.permission_name,
               b.permission_value,
               b.permission_type,
               b.permission_business_type,
               b.login_port_type
        from frt_tenant_role_permission a
                 left join frt_basic_permission b on a.permission_id = b.permission_id
        where a.tenant_id = #{tenantId, jdbcType=VARCHAR}
          and a.role_id = #{roleId, jdbcType=VARCHAR}
          and b.is_enabled = 1
    </select>
</mapper>
