<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.frt.usercore.dao.mapper.TenantRoleMapper">

    <select id="findPageList" resultType="com.frt.usercore.dao.entity.TenantRoleDO">
        SELECT * FROM frt_tenant_role WHERE
        tenant_id = #{query.tenantId, jdbcType=VARCHAR}
        <if test="query.roleName != null and query.roleName != ''">
            AND role_name LIKE CONCAT('%', #{query.roleName, jdbcType=VARCHAR}, '%')
        </if>
        and status = 1
        and role_type = #{query.terminalType, jdbcType=INTEGER}
        and is_del = 0
    </select>
    <select id="findRoleByUserIdList"
            resultType="com.frt.usercore.domain.dto.result.FindRoleByUserIdListResultDTO">
        SELECT t1.role_id, t1.role_name, t1.user_id FROM frt_tenant_role t1
        LEFT JOIN frt_account_bind_role t2 ON t1.role_id = t2.role_id
        WHERE t2.user_id IN
        <foreach item="item" index="index" collection="userIdList" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        and t1.is_del = 0
        and t2.is_del = 0
    </select>
</mapper>
